using Compass.Common.Data;
using Compass.Common.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using System.Security.Claims;
using System.Text.Json;

namespace Compass.Common.Services
{
    public class UserAssignmentRequest
    {
        public string UserId { get; set; } = string.Empty;
        public ResourceType ResourceType { get; set; }
        public long ResourceId { get; set; }
        public string UserRole { get; set; } = string.Empty;
        public Dictionary<PermissionType, bool> Permissions { get; set; } = new();
        public string LinkStatus { get; set; } = "Active";
    }

    public class UserAssignmentResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public long? AccessId { get; set; }
        public long? LinkId { get; set; }
    }

    public interface IUserManagementService
    {
        Task<UserAssignmentResult> AssignUserToResourceAsync(UserAssignmentRequest request);
        Task<bool> RemoveUserFromResourceAsync(string userId, ResourceType resourceType, long resourceId);
        Task<bool> UpdateUserPermissionsAsync(string userId, ResourceType resourceType, long resourceId, Dictionary<PermissionType, bool> permissions);
        Task<List<ApplicationUser>> GetUsersWithAccessToResourceAsync(ResourceType resourceType, long resourceId);
        Task<bool> TransferUserAccessAsync(string fromUserId, string toUserId, ResourceType resourceType, long resourceId);
        Task<bool> BulkAssignUsersAsync(List<UserAssignmentRequest> requests);
        Task<Dictionary<ResourceType, List<long>>> GetUserAccessSummaryAsync(string userId);
    }

    public class UserManagementService : IUserManagementService
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IPermissionService _permissionService;
        private readonly ILogger<UserManagementService> _logger;
        private readonly IDistributedCache _distributedCache;

        public UserManagementService(
            IDbContextFactory<ApplicationDbContext> contextFactory,
            UserManager<ApplicationUser> userManager,
            IPermissionService permissionService,
            ILogger<UserManagementService> logger,
            IDistributedCache distributedCache)
        {
            _contextFactory = contextFactory;
            _userManager = userManager;
            _permissionService = permissionService;
            _logger = logger;
            _distributedCache = distributedCache;
        }

        public async Task<UserAssignmentResult> AssignUserToResourceAsync(UserAssignmentRequest request)
        {
            try
            {
                using var context = await _contextFactory.CreateDbContextAsync();
                using var transaction = await context.Database.BeginTransactionAsync();

                try
                {
                    var result = new UserAssignmentResult();

                    // Validate user exists
                    var user = await _userManager.FindByIdAsync(request.UserId);
                    if (user == null)
                    {
                        result.Message = "User not found";
                        return result;
                    }

                    // Create access record
                    var accessId = await CreateAccessRecordAsync(context, request);
                    if (accessId == null)
                    {
                        result.Message = "Failed to create access record";
                        return result;
                    }

                    // Create link record
                    var linkId = await CreateLinkRecordAsync(context, request, accessId.Value);
                    if (linkId == null)
                    {
                        result.Message = "Failed to create link record";
                        return result;
                    }

                    await transaction.CommitAsync();

                    // Invalidate user permissions cache
                    await _permissionService.InvalidateUserPermissionsAsync(request.UserId);

                    result.Success = true;
                    result.AccessId = accessId;
                    result.LinkId = linkId;
                    result.Message = "User successfully assigned to resource";

                    _logger.LogInformation("User {UserId} assigned to {ResourceType} {ResourceId} with role {Role}",
                        request.UserId, request.ResourceType, request.ResourceId, request.UserRole);

                    return result;
                }
                catch (Exception)
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error assigning user {UserId} to {ResourceType} {ResourceId}",
                    request.UserId, request.ResourceType, request.ResourceId);
                return new UserAssignmentResult { Message = "An error occurred during assignment" };
            }
        }

        private async Task<long?> CreateAccessRecordAsync(ApplicationDbContext context, UserAssignmentRequest request)
        {
            var currentUserId = GetCurrentUserId();
            var now = DateTime.UtcNow;

            switch (request.ResourceType)
            {
                case ResourceType.Organization:
                    var orgAccess = new UserOrganizationAccess
                    {
                        OrganizationId = request.ResourceId,
                        ModId = currentUserId,
                        ModTs = now,
                        CanAdd = request.Permissions.GetValueOrDefault(PermissionType.CanAdd) ? "Y" : "N",
                        CanUpdate = request.Permissions.GetValueOrDefault(PermissionType.CanUpdate) ? "Y" : "N",
                        CanDelete = request.Permissions.GetValueOrDefault(PermissionType.CanDelete) ? "Y" : "N",
                        CanView = request.Permissions.GetValueOrDefault(PermissionType.CanView) ? "Y" : "N",
                        CanAssign = request.Permissions.GetValueOrDefault(PermissionType.CanAssign) ? "Y" : "N"
                    };
                    context.UserOrganizationAccesses.Add(orgAccess);
                    await context.SaveChangesAsync();
                    return orgAccess.Id;

                case ResourceType.Entity1:
                    var entity1Access = new UserEntity1Access
                    {
                        OrganizationId = await GetOrganizationIdForEntity1Async(context, request.ResourceId),
                        Entity1Id = request.ResourceId,
                        ModId = currentUserId,
                        ModTs = now,
                        CanAdd = request.Permissions.GetValueOrDefault(PermissionType.CanAdd) ? "Y" : "N",
                        CanUpdate = request.Permissions.GetValueOrDefault(PermissionType.CanUpdate) ? "Y" : "N",
                        CanDelete = request.Permissions.GetValueOrDefault(PermissionType.CanDelete) ? "Y" : "N",
                        CanView = request.Permissions.GetValueOrDefault(PermissionType.CanView) ? "Y" : "N",
                        CanAssign = request.Permissions.GetValueOrDefault(PermissionType.CanAssign) ? "Y" : "N"
                    };
                    context.UserEntity1Accesses.Add(entity1Access);
                    await context.SaveChangesAsync();
                    return entity1Access.Id;

                case ResourceType.Entity2:
                    var entity2Access = new UserEntity2Access
                    {
                        OrganizationId = await GetOrganizationIdForEntity2Async(context, request.ResourceId),
                        Entity2Id = request.ResourceId,
                        ModId = currentUserId,
                        ModTs = now,
                        CanAdd = request.Permissions.GetValueOrDefault(PermissionType.CanAdd) ? "Y" : "N",
                        CanUpdate = request.Permissions.GetValueOrDefault(PermissionType.CanUpdate) ? "Y" : "N",
                        CanDelete = request.Permissions.GetValueOrDefault(PermissionType.CanDelete) ? "Y" : "N",
                        CanView = request.Permissions.GetValueOrDefault(PermissionType.CanView) ? "Y" : "N",
                        CanAssign = request.Permissions.GetValueOrDefault(PermissionType.CanAssign) ? "Y" : "N"
                    };
                    context.UserEntity2Accesses.Add(entity2Access);
                    await context.SaveChangesAsync();
                    return entity2Access.Id;

                case ResourceType.Entity3:
                    var entity3Access = new UserEntity3Access
                    {
                        OrganizationId = await GetOrganizationIdForEntity3Async(context, request.ResourceId),
                        Entity3Id = request.ResourceId,
                        ModId = currentUserId,
                        ModTs = now,
                        CanAdd = request.Permissions.GetValueOrDefault(PermissionType.CanAdd) ? "Y" : "N",
                        CanUpdate = request.Permissions.GetValueOrDefault(PermissionType.CanUpdate) ? "Y" : "N",
                        CanDelete = request.Permissions.GetValueOrDefault(PermissionType.CanDelete) ? "Y" : "N",
                        CanView = request.Permissions.GetValueOrDefault(PermissionType.CanView) ? "Y" : "N",
                        CanAssign = request.Permissions.GetValueOrDefault(PermissionType.CanAssign) ? "Y" : "N"
                    };
                    context.UserEntity3Accesses.Add(entity3Access);
                    await context.SaveChangesAsync();
                    return entity3Access.Id;

                case ResourceType.Site:
                    var siteAccess = new UserSiteAccess
                    {
                        OrganizationId = await GetOrganizationIdForSiteAsync(context, request.ResourceId),
                        SiteId = request.ResourceId,
                        ModId = currentUserId,
                        ModTs = now,
                        CanAdd = request.Permissions.GetValueOrDefault(PermissionType.CanAdd) ? "Y" : "N",
                        CanUpdate = request.Permissions.GetValueOrDefault(PermissionType.CanUpdate) ? "Y" : "N",
                        CanDelete = request.Permissions.GetValueOrDefault(PermissionType.CanDelete) ? "Y" : "N",
                        CanView = request.Permissions.GetValueOrDefault(PermissionType.CanView) ? "Y" : "N",
                        CanAssign = request.Permissions.GetValueOrDefault(PermissionType.CanAssign) ? "Y" : "N"
                    };
                    context.UserSiteAccesses.Add(siteAccess);
                    await context.SaveChangesAsync();
                    return siteAccess.Id;

                case ResourceType.StudentGroup:
                    var studentGroupAccess = new UserStudentGroupAccess
                    {
                        OrganizationId = await GetOrganizationIdForStudentGroupAsync(context, request.ResourceId),
                        StudentGroupId = request.ResourceId,
                        ModId = currentUserId,
                        ModTs = now,
                        CanAdd = request.Permissions.GetValueOrDefault(PermissionType.CanAdd) ? "Y" : "N",
                        CanUpdate = request.Permissions.GetValueOrDefault(PermissionType.CanUpdate) ? "Y" : "N",
                        CanDelete = request.Permissions.GetValueOrDefault(PermissionType.CanDelete) ? "Y" : "N",
                        CanView = request.Permissions.GetValueOrDefault(PermissionType.CanView) ? "Y" : "N",
                        CanAssign = request.Permissions.GetValueOrDefault(PermissionType.CanAssign) ? "Y" : "N"
                    };
                    context.UserStudentGroupAccesses.Add(studentGroupAccess);
                    await context.SaveChangesAsync();
                    return studentGroupAccess.Id;

                default:
                    return null;
            }
        }