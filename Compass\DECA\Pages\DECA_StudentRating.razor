@page "/deca-student-rating"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Forms
@using Compass.Deca.Models

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

<h3>Preschool Behavior Questionnaire</h3>

@if (isLoading)
{
    <div class="d-flex justify-content-center">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
}
else if (questions != null && questions.Any())
{
    <EditForm Model="responses" OnValidSubmit="HandleValidSubmit">
        <table class="table table-bordered table-striped">
            <thead class="table-light">
                <tr>
                    <th style="width: 40px;">Item#</th>
                    <th>During the past 4 weeks, how often did the preschooler...</th>
                    <th class="text-center">Never</th>
                    <th class="text-center">Rarely</th>
                    <th class="text-center">Occasionally</th>
                    <th class="text-center">Frequently</th>
                    <th class="text-center">Very Frequently</th>
                </tr>
            </thead>
            <tbody>
                @for (int i = 0; i < questions.Count; i++)
                {
                    int questionIndex = i; // Capture the loop variable
                    <tr>
                        <td>@(questionIndex + 1)</td>
                        <td>@questions[questionIndex].TextEnglish</td>
                        <InputRadioGroup @bind-Value="responses[questionIndex]">
                            @foreach ((int Value, string Label) option in options)
                            {
                                <td class="text-center align-middle">
                                    <InputRadio Value="@option.Value" />
                                </td>
                            }
                        </InputRadioGroup>
                    </tr>
                }
            </tbody>
        </table>
        <button type="submit" class="btn btn-primary">Submit</button>
    </EditForm>
}
else
{
    <div class="alert alert-info">
        <i class="bi bi-info-circle"></i>
        No questions found.
    </div>
}