using Compass.Common.Data;
using Compass.Common.Resources;
using Compass.Common.Services;
using Compass.Common.SessionHandlers;
using Compass.Deca.Models;
using Compass.DECA.Interfaces.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Localization;

namespace Compass.DECA.Pages
{
    public partial class DECA_StudentRating : IDisposable
    {
        [Inject]
        public required IDecaQuestionService DecaQuestionService { get; set; }
        [Inject]
        public required UserManager<ApplicationUser> UserManager { get; set; }
        [Inject]
        public required AuthenticationStateProvider AuthenticationStateProvider { get; set; }
        [Inject]
        public required UserSessionService UserSessionService { get; set; }
        [Inject]
        public required UserAccessor UserAccessor { get; set; }
        [Inject]
        public required IStringLocalizer<CommonResource> Localizer { get; set; }
        [Inject]
        public required CurrentCultureObserver CurrentLanguageObserver { get; set; }
        [Inject]
        public required CultureService CultureService { get; set; }


        private bool isLoading = true;
        private string? _currentUserId;
        private ApplicationUser? _currentUser;
        private long? _currentStudentId;

        private List<DecaQuestion> questions = new();
        private List<int?> responses = new();

        private readonly List<(int Value, string Label)> options = new()
        {
            (1, "Never"),
            (2, "Rarely"),
            (3, "Occasionally"),
            (4, "Frequently"),
            (5, "Very Frequently")
        };

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            (ApplicationUser? user, string? userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            isLoading = true;
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);
            CommonSessionData? commonSessionData = await GetCommonSessionData();

            if (commonSessionData is not null)
            {
                _currentStudentId = commonSessionData.CurrentStudentId;
                if ( _currentStudentId.HasValue)
                {
                    questions = await DecaQuestionService.GetByRecordFormAsync("P");
                    responses = new List<int?>(new int?[questions.Count]);
                }

                isLoading = false;
            }
        }

        private void UpdateLocalizedValues()
        {
            string culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }

        private void HandleValidSubmit()
        {
            // TODO: Save responses to backend or process as needed
        }
    }
}
